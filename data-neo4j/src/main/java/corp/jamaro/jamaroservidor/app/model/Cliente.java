package corp.jamaro.jamaroservidor.app.model;

import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;

import java.util.UUID;

@Data
@Node
public class Cliente {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private String nombre;
    private String apellido;
    private String razonSocial;

    private String dni;
    private String ruc;
    private String otroDocumento;

    private String direccion;
    private String telefono;
    private String email;

    private Boolean tieneCredito = false;
    private Boolean esMayorista = false;

    private Boolean esProveedor = false;

    private Boolean estado = true;

    private String metadata;

}
